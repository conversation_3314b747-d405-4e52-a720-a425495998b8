from flask import Blueprint, render_template, request, jsonify
from app.models.database import get_db
from app.utils.helpers import hash_password
from datetime import datetime
import uuid

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
def index():
    """管理员系统首页"""
    return render_template("admin/admin.html")

@admin_bp.route('/add_student', methods=['POST'])
def add_student():
    """增加学生"""
    try:
        student = request.get_json()
        if not student:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        # 验证必要字段
        required_fields = ['student_id', 'name', 'gender', 'class_id']
        for field in required_fields:
            if field not in student or not student[field]:
                return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

        conn = get_db()
        try:
            cursor = conn.cursor()

            # 检查学号是否已存在
            cursor.execute("SELECT student_id FROM students WHERE student_id = ?", (student['student_id'],))
            if cursor.fetchone():
                return jsonify({"status": "error", "message": "学号已存在"}), 400

            # 检查班级是否存在
            cursor.execute("SELECT id FROM classes WHERE id = ?", (student['class_id'],))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "指定的班级不存在"}), 400

            # 设置默认密码为123456
            default_password = hash_password("123456")

            cursor.execute(
                "INSERT INTO students (student_id, name, password, gender, class_id, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                (
                    student['student_id'],
                    student['name'],
                    default_password,
                    student['gender'],
                    student['class_id'],
                    datetime.now().isoformat()
                )
            )
            conn.commit()
        finally:
            conn.close()

        return jsonify({"status": "success", "message": "学生添加成功"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_classes', methods=['GET'])
def get_classes():
    """获取班级列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name, grade FROM classes ORDER BY grade, name")
        classes = cursor.fetchall()

        class_list = []
        for cls in classes:
            class_list.append({
                "id": cls[0],
                "name": cls[1],
                "grade": cls[2]
            })

        conn.close()
        return jsonify({"status": "success", "classes": class_list})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_courses', methods=['GET'])
def get_courses():
    """获取课程列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name, code FROM courses ORDER BY name")
        courses = cursor.fetchall()

        course_list = []
        for course in courses:
            course_list.append({
                "id": course[0],
                "name": course[1],
                "code": course[2]
            })

        conn.close()
        return jsonify({"status": "success", "courses": course_list})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_classrooms', methods=['GET'])
def get_classrooms():
    """获取教室列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name, capacity FROM classrooms ORDER BY name")
        classrooms = cursor.fetchall()

        classroom_list = []
        for classroom in classrooms:
            classroom_list.append({
                "id": classroom[0],
                "name": classroom[1],
                "capacity": classroom[2]
            })

        conn.close()
        return jsonify({"status": "success", "classrooms": classroom_list})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_teachers', methods=['GET'])
def get_teachers():
    """获取教师列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("SELECT teacher_id, name FROM teachers ORDER BY name")
        teachers = cursor.fetchall()

        teacher_list = []
        for teacher in teachers:
            teacher_list.append({
                "teacher_id": teacher[0],
                "name": teacher[1]
            })

        conn.close()
        return jsonify({"status": "success", "teachers": teacher_list})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/add_course_schedule', methods=['POST'])
def add_course_schedule():
    """添加课程安排"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        # 验证必要字段
        required_fields = ['course_id', 'teacher_id', 'classroom_id', 'class_id',
                          'day_of_week', 'start_time', 'end_time']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

        conn = get_db()
        try:
            cursor = conn.cursor()

            # 验证课程、教师、教室、班级是否存在
            cursor.execute("SELECT id FROM courses WHERE id = ?", (data['course_id'],))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "指定的课程不存在"}), 400

            cursor.execute("SELECT teacher_id FROM teachers WHERE teacher_id = ?", (data['teacher_id'],))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "指定的教师不存在"}), 400

            cursor.execute("SELECT id FROM classrooms WHERE id = ?", (data['classroom_id'],))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "指定的教室不存在"}), 400

            cursor.execute("SELECT id FROM classes WHERE id = ?", (data['class_id'],))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "指定的班级不存在"}), 400

            # 生成唯一ID
            schedule_id = str(uuid.uuid4())

            # 插入课程安排
            cursor.execute("""
                INSERT INTO course_schedules (
                    id, course_id, teacher_id, classroom_id, class_id,
                    day_of_week, start_time, end_time, status, description, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                schedule_id,
                data['course_id'],
                data['teacher_id'],
                data['classroom_id'],
                data['class_id'],
                data['day_of_week'],
                data['start_time'],
                data['end_time'],
                'scheduled',
                data.get('description', ''),
                datetime.now().isoformat()
            ))

            # 获取指定班级的所有学生
            cursor.execute("SELECT student_id FROM students WHERE class_id = ?", (data['class_id'],))
            students = cursor.fetchall()

            # 为每个学生创建未签到的签到记录
            for student in students:
                attendance_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO class_attendance (
                        id, course_schedule_id, student_id, signin_time, status, remark, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    attendance_id,
                    schedule_id,
                    student[0],
                    '',  # 空的签到时间，表示未签到
                    'absent',  # 默认为缺席状态
                    '系统自动创建',
                    datetime.now().isoformat()
                ))

            conn.commit()
        finally:
            conn.close()

        return jsonify({"status": "success", "message": "课程安排添加成功", "id": schedule_id})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_course_schedules', methods=['GET'])
def get_course_schedules():
    """获取课程安排列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   t.name as teacher_name, cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN teachers t ON cs.teacher_id = t.teacher_id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            ORDER BY cs.created_at DESC, cs.day_of_week, cs.start_time
        """)
        schedules = cursor.fetchall()

        schedule_list = []
        for schedule in schedules:
            schedule_list.append({
                "id": schedule[0],
                "course_name": schedule[1],
                "course_code": schedule[2],
                "teacher_name": schedule[3],
                "classroom_name": schedule[4],
                "class_name": schedule[5],
                "day_of_week": schedule[6],
                "start_time": schedule[7],
                "end_time": schedule[8],
                "status": schedule[9],
                "description": schedule[10]
            })

        conn.close()
        return jsonify({"status": "success", "schedules": schedule_list})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_schedule_attendance/<schedule_id>', methods=['GET'])
def get_schedule_attendance(schedule_id):
    """获取指定课程安排的学生签到情况"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.student_id, s.name, s.gender, ca.signin_time, ca.status, ca.remark
            FROM students s
            JOIN class_attendance ca ON s.student_id = ca.student_id
            WHERE ca.course_schedule_id = ?
            ORDER BY s.name
        """, (schedule_id,))

        attendance_records = cursor.fetchall()

        attendance_list = []
        for record in attendance_records:
            attendance_list.append({
                "student_id": record[0],
                "student_name": record[1],
                "gender": record[2],
                "signin_time": record[3],
                "status": record[4],
                "remark": record[5]
            })

        conn.close()
        return jsonify({"status": "success", "attendance": attendance_list})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# 课程管理相关API
@admin_bp.route('/add_course', methods=['POST'])
def add_course():
    """添加课程"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        # 验证必要字段
        required_fields = ['name', 'code']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

        conn = get_db()
        try:
            cursor = conn.cursor()

            # 检查课程代码是否已存在
            cursor.execute("SELECT id FROM courses WHERE code = ?", (data['code'],))
            if cursor.fetchone():
                return jsonify({"status": "error", "message": "课程代码已存在"}), 400

            # 生成唯一ID
            course_id = str(uuid.uuid4())

            # 插入课程
            cursor.execute("""
                INSERT INTO courses (id, name, code, description, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                course_id,
                data['name'],
                data['code'],
                data.get('description', ''),
                datetime.now().isoformat()
            ))

            conn.commit()
        finally:
            conn.close()

        return jsonify({"status": "success", "message": "课程添加成功", "id": course_id})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_course/<course_id>', methods=['GET'])
def get_course(course_id):
    """获取课程详情"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name, code, description FROM courses WHERE id = ?", (course_id,))
        course = cursor.fetchone()

        if not course:
            conn.close()
            return jsonify({"status": "error", "message": "课程不存在"}), 404

        course_data = {
            "id": course[0],
            "name": course[1],
            "code": course[2],
            "description": course[3]
        }

        conn.close()
        return jsonify({"status": "success", "course": course_data})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/update_course/<course_id>', methods=['PUT'])
def update_course(course_id):
    """更新课程"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        # 验证必要字段
        required_fields = ['name', 'code']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

        conn = get_db()
        try:
            cursor = conn.cursor()

            # 检查课程是否存在
            cursor.execute("SELECT id FROM courses WHERE id = ?", (course_id,))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "课程不存在"}), 404

            # 检查课程代码是否被其他课程使用
            cursor.execute("SELECT id FROM courses WHERE code = ? AND id != ?", (data['code'], course_id))
            if cursor.fetchone():
                return jsonify({"status": "error", "message": "课程代码已被其他课程使用"}), 400

            # 更新课程
            cursor.execute("""
                UPDATE courses
                SET name = ?, code = ?, description = ?
                WHERE id = ?
            """, (
                data['name'],
                data['code'],
                data.get('description', ''),
                course_id
            ))

            conn.commit()
        finally:
            conn.close()

        return jsonify({"status": "success", "message": "课程更新成功"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/delete_course/<course_id>', methods=['DELETE'])
def delete_course(course_id):
    """删除课程"""
    try:
        conn = get_db()
        try:
            cursor = conn.cursor()

            # 检查课程是否存在
            cursor.execute("SELECT id FROM courses WHERE id = ?", (course_id,))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "课程不存在"}), 404

            # 查找与该课程关联的所有课程安排
            cursor.execute("SELECT id FROM course_schedules WHERE course_id = ?", (course_id,))
            schedules = cursor.fetchall()

            # 删除每个课程安排关联的签到记录
            for schedule in schedules:
                schedule_id = schedule[0]
                cursor.execute("DELETE FROM class_attendance WHERE course_schedule_id = ?", (schedule_id,))

            # 删除与该课程关联的所有课程安排
            cursor.execute("DELETE FROM course_schedules WHERE course_id = ?", (course_id,))

            # 删除课程
            cursor.execute("DELETE FROM courses WHERE id = ?", (course_id,))

            conn.commit()
        finally:
            conn.close()

        return jsonify({"status": "success", "message": "课程删除成功"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/get_courses_with_details', methods=['GET'])
def get_courses_with_details():
    """获取课程列表（包含详细信息）"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT c.id, c.name, c.code, c.description, c.created_at,
                   COUNT(cs.id) as schedule_count
            FROM courses c
            LEFT JOIN course_schedules cs ON c.id = cs.course_id
            GROUP BY c.id, c.name, c.code, c.description, c.created_at
            ORDER BY c.name
        """)
        courses = cursor.fetchall()

        course_list = []
        for course in courses:
            course_list.append({
                "id": course[0],
                "name": course[1],
                "code": course[2],
                "description": course[3],
                "created_at": course[4],
                "schedule_count": course[5]
            })

        conn.close()
        return jsonify({"status": "success", "courses": course_list})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@admin_bp.route('/delete_course_schedule/<schedule_id>', methods=['DELETE'])
def delete_course_schedule(schedule_id):
    """删除课程安排，并级联删除所有关联记录"""
    try:
        conn = get_db()
        try:
            cursor = conn.cursor()

            # 检查课程安排是否存在
            cursor.execute("SELECT id FROM course_schedules WHERE id = ?", (schedule_id,))
            if not cursor.fetchone():
                return jsonify({"status": "error", "message": "课程安排不存在"}), 404

            # 1. 删除关联的签到记录
            cursor.execute("DELETE FROM class_attendance WHERE course_schedule_id = ?", (schedule_id,))

            # 2. 删除关联的学生作业
            # cursor.execute("DELETE FROM student_homework WHERE homework_id IN (SELECT id FROM homework WHERE course_schedule_id = ?)", (schedule_id,))

            # 3. 删除关联的作业
            cursor.execute("DELETE FROM homework WHERE course_schedule_id = ?", (schedule_id,))

            # 4. 删除关联的试卷题目
            # cursor.execute("DELETE FROM paper_questions WHERE paper_id IN (SELECT id FROM papers WHERE course_schedule_id = ?)", (schedule_id,))

            # 5. 删除关联的试卷
            cursor.execute("DELETE FROM papers WHERE course_schedule_id = ?", (schedule_id,))
            
            # 6. 删除课程安排本身
            cursor.execute("DELETE FROM course_schedules WHERE id = ?", (schedule_id,))

            conn.commit()
        finally:
            conn.close()

        return jsonify({"status": "success", "message": "课程安排及所有关联数据删除成功"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500
